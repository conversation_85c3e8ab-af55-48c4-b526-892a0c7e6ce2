{"roots": ["rally_championship_manager"], "packages": [{"name": "rally_championship_manager", "version": "1.0.0+1", "dependencies": ["cupertino_icons", "flutter", "json_annotation"], "devDependencies": ["build_runner", "flutter_lints", "flutter_test", "json_serializable"]}, {"name": "build_runner", "version": "2.7.0", "dependencies": ["args", "async", "build", "build_config", "build_daemon", "build_runner_core", "code_builder", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http_multi_server", "io", "logging", "mime", "path", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web_socket_channel"]}, {"name": "json_serializable", "version": "6.10.0", "dependencies": ["analyzer", "async", "build", "build_config", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["clock", "collection", "fake_async", "flutter", "leak_tracker_flutter_testing", "matcher", "meta", "path", "stack_trace", "stream_channel", "test_api", "vector_math"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "3.1.1", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "9.3.0", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.2.0", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse"]}, {"name": "build", "version": "3.0.2", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "source_helper", "version": "1.3.7", "dependencies": ["analyzer", "source_gen"]}, {"name": "source_gen", "version": "3.1.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "analyzer", "version": "7.7.1", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "leak_tracker_flutter_testing", "version": "3.0.10", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.2.0", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "built_value", "version": "8.11.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "build_resolvers", "version": "3.0.2", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "package_config", "path", "pool", "pub_semver"]}, {"name": "_fe_analyzer_shared", "version": "85.0.0", "dependencies": ["meta"]}, {"name": "leak_tracker_testing", "version": "3.0.2", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "11.0.1", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}], "configVersion": 1}